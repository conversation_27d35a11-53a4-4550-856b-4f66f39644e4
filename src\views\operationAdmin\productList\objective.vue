<template>
  <div class="app-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <!-- <el-button icon="el-icon-arrow-left" @click="goBack">返回</el-button>
        <h2 class="page-title">目标详情</h2> -->
        <div class="date-picker-container">
          <el-date-picker
            v-model="dateRange.startDate"
            type="date"
            placeholder="开始日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            style="margin-right: 10px;"
          />
          <el-date-picker
            v-model="dateRange.endDate"
            type="date"
            placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            style="margin-right: 10px;"
          />
          <el-button type="primary" @click="loadDetailData">查询</el-button>
        </div>
      </div>
      <div class="header-right">
        <el-button type="success" icon="el-icon-download" @click="exportData">导出</el-button>
      </div>
    </div>

    <!-- 产品基本信息 -->
    <div class="product-info-card">
      <h3>产品名称：{{ (productInfo && productInfo.localName ) }}</h3>
      <div class="info-row">
        <div class="info-item">
          <span class="label">ASIN:</span>
          <span class="value">{{ productInfo && productInfo.asin }}</span>
        </div>
        <div class="info-item">
          <span class="label">MSKU:</span>
          <span class="value">{{ productInfo && productInfo.sellerSku }}</span>
        </div>
        <div class="info-item">
          <span class="label">店铺:</span>
          <span class="value">{{ productInfo && productInfo.sellerName }}</span>
        </div>
        <div class="info-item">
          <span class="label">国家:</span>
          <span class="value">{{ productInfo && productInfo.country }}</span>
        </div>
      </div>
    </div>

    <!-- 时间选择器 -->
    <!-- <div class="date-picker-card"> -->
    <!-- <div class="date-picker-container">
        <el-date-picker
          v-model="dateRange.startDate"
          type="date"
          placeholder="开始日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          style="margin-right: 10px;"
        />
        <el-date-picker
          v-model="dateRange.endDate"
          type="date"
          placeholder="结束日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          style="margin-right: 10px;"
        />
        <el-button type="primary" @click="loadDetailData">查询</el-button>
      </div> -->
    <!-- </div> -->

    <!-- 数据展示区域 -->
    <div v-loading="detailLoading" class="detail-content">
      <div v-if="detailData && detailData.data && Array.isArray(detailData.data) && detailData.data.length > 0">
        <!-- 复盘时间表格 -->
        <!-- <div class="table-section">
          <h4>复盘时间</h4>
          <el-table :data="timeTableData" border style="width: 100%" @cell-dblclick="cellEdit">
            <el-table-column prop="label" label="时间" width="150" />
            <el-table-column
              v-for="(period, index) in timePeriods"
              :key="`time-${index}`"
              :prop="period.key"
              :label="period.label"
            >
              <template slot-scope="scope">
                <el-input
                  v-if="scope.row.isEdit && editingCell.column === period.key"
                  :ref="period.key"
                  v-model="scope.row[period.key]"
                  clearable
                  @keyup.enter.native="onBlur(scope.row, period.key)"
                  @blur="onBlur(scope.row, period.key)"
                />
                <span v-else>{{ scope.row[period.key] }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div> -->

        <!-- 销售数据表格 -->
        <div class="table-section">
          <h4>销售数据</h4>
          <el-table :data="salesTableData" border style="width: 100%" @cell-dblclick="cellEdit">
            <el-table-column prop="label" label="指标" width="150" />
            <el-table-column
              v-for="(period, index) in timePeriods"
              :key="`sales-${index}`"
              :prop="period.key"
              :label="period.label"
            >
              <template slot-scope="scope">
                <el-input
                  v-if="scope.row.isEdit && editingCell.column === period.key"
                  :ref="period.key"
                  v-model="scope.row[period.key]"
                  clearable
                  @keyup.enter.native="onBlur(scope.row, period.key)"
                  @blur="onBlur(scope.row, period.key)"
                />
                <span
                  v-else
                  :class="{ 'editable-cell': isFieldEditable(scope.row.fieldKey) }"
                >{{ scope.row[period.key] }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 自然流量表格 -->
        <div class="table-section">
          <h4>自然流量</h4>
          <el-table :data="organicTableData" border style="width: 100%" @cell-dblclick="cellEdit">
            <el-table-column prop="label" label="指标" width="150" />
            <el-table-column
              v-for="(period, index) in timePeriods"
              :key="`organic-${index}`"
              :prop="period.key"
              :label="period.label"
            >
              <template slot-scope="scope">
                <el-input
                  v-if="scope.row.isEdit && editingCell.column === period.key"
                  :ref="period.key"
                  v-model="scope.row[period.key]"
                  clearable
                  @keyup.enter.native="onBlur(scope.row, period.key)"
                  @blur="onBlur(scope.row, period.key)"
                />
                <span
                  v-else
                  :class="{ 'editable-cell': isFieldEditable(scope.row.fieldKey) }"
                >{{ scope.row[period.key] }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 广告数据表格 -->
        <div class="table-section">
          <h4>广告数据</h4>
          <el-table :data="adTableData" border style="width: 100%" @cell-dblclick="cellEdit">
            <el-table-column prop="label" label="指标" width="150" />
            <el-table-column
              v-for="(period, index) in timePeriods"
              :key="`ad-${index}`"
              :prop="period.key"
              :label="period.label"
            >
              <template slot-scope="scope">
                <el-input
                  v-if="scope.row.isEdit && editingCell.column === period.key"
                  :ref="period.key"
                  v-model="scope.row[period.key]"
                  clearable
                  @keyup.enter.native="onBlur(scope.row, period.key)"
                  @blur="onBlur(scope.row, period.key)"
                />
                <span
                  v-else
                  :class="{ 'editable-cell': isFieldEditable(scope.row.fieldKey) }"
                >{{ scope.row[period.key] }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 利润数据表格 -->
        <div class="table-section">
          <h4>利润数据</h4>
          <el-table :data="profitTableData" border style="width: 100%" @cell-dblclick="cellEdit">
            <el-table-column prop="label" label="指标" width="150" />
            <el-table-column
              v-for="(period, index) in timePeriods"
              :key="`profit-${index}`"
              :prop="period.key"
              :label="period.label"
            >
              <template slot-scope="scope">
                <el-input
                  v-if="scope.row.isEdit && editingCell.column === period.key"
                  :ref="period.key"
                  v-model="scope.row[period.key]"
                  clearable
                  @keyup.enter.native="onBlur(scope.row, period.key)"
                  @blur="onBlur(scope.row, period.key)"
                />
                <span
                  v-else
                  :class="{ 'editable-cell': isFieldEditable(scope.row.fieldKey) }"
                >{{ scope.row[period.key] }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- Listing数据表格 -->
        <div class="table-section">
          <h4>Listing数据</h4>
          <el-table :data="listingTableData" border style="width: 100%" @cell-dblclick="cellEdit">
            <el-table-column prop="label" label="指标" width="150" />
            <el-table-column
              v-for="(period, index) in timePeriods"
              :key="`listing-${index}`"
              :prop="period.key"
              :label="period.label"
            >
              <template slot-scope="scope">
                <el-input
                  v-if="scope.row.isEdit && editingCell.column === period.key"
                  :ref="period.key"
                  v-model="scope.row[period.key]"
                  clearable
                  @keyup.enter.native="onBlur(scope.row, period.key)"
                  @blur="onBlur(scope.row, period.key)"
                />
                <span
                  v-else
                  :class="{ 'editable-cell': isFieldEditable(scope.row.fieldKey) }"
                >{{ scope.row[period.key] }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div v-else-if="!detailLoading" class="no-data">
        <div v-if="detailData && detailData.success === false">
          获取数据失败，请重试
        </div>
        <div v-else>
          暂无数据
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getWeeklyReviewDetail, exportWeeklyReview } from '@/api/operationAdmin/productList'
import { downloadFile } from '@/utils'

export default {
  name: 'ObjectiveDetail',
  data() {
    return {
      detailLoading: false,
      productInfo: null,
      dateRange: {
        startDate: '',
        endDate: ''
      },
      detailData: null,
      // 可编辑字段列表
      editableColumns: [],
      // 编辑相关数据
      editingCell: {
        row: null,
        column: null
      }
    }
  },
  computed: {
    // 时间周期数据
    timePeriods() {
      if (!this.detailData || !this.detailData.data || !Array.isArray(this.detailData.data) || this.detailData.data.length === 0) {
        return []
      }

      // 根据数据数组生成时间周期
      return this.detailData.data.map((item, index) => ({
        key: `period_${index}`,
        label: item.timePeriod || `周期${index + 1}`,
        dataIndex: index
      }))
    },

    // 时间表格数据
    timeTableData() {
      if (!this.timePeriods.length) return []

      const timeRow = { label: '时间' }
      this.timePeriods.forEach(period => {
        timeRow[period.key] = period.label
      })

      return [timeRow]
    },

    // 销售数据表格
    salesTableData() {
      if (!this.detailData || !this.detailData.data || !Array.isArray(this.detailData.data)) return []

      const salesMetrics = [
        { key: 'targetDailySalesAmount', label: '目标销售额（周）' },
        { key: 'actualWeeklySales', label: '实际销售额（周）' },
        { key: 'actualOrders', label: '实际订单数' },
        { key: 'salesVolume', label: '销售数量' },
        { key: 'totalWeeklySalesAmount', label: '总销售额' },
        { key: 'actualAveragePrice', label: '实际销售均价' },
        { key: 'averageDailyPrice', label: '平均日价格' },
        { key: 'averageDailySales', label: '平均日销量' }
        // { key: 'actualWeeklyVisits', label: '买家访问次数（周）' }
      ]

      return salesMetrics.map(metric => {
        const row = {
          label: metric.label,
          fieldKey: metric.key // 添加字段标识
        }
        this.timePeriods.forEach(period => {
          const dataItem = this.detailData.data[period.dataIndex]
          row[period.key] = dataItem ? (dataItem[metric.key] || 0) : 0
        })
        return row
      })
    },

    // 自然流量数据表格
    organicTableData() {
      if (!this.detailData || !this.detailData.data || !Array.isArray(this.detailData.data)) return []

      const organicMetrics = [
        { key: 'actualWeeklyVisits', label: '买家访问次数（周）' },
        { key: 'conversionRate', label: 'CVR转化率' },
        { key: 'organicSales', label: '自然销量' },
        { key: 'organicOrders', label: '自然订单' },
        { key: 'organicCVR', label: '自然CVR' },
        { key: 'organicOrderRatio', label: '自然订单占比' },
        { key: 'favoriteVisitors', label: '收藏访客数' }
      ]

      return organicMetrics.map(metric => {
        const row = {
          label: metric.label,
          fieldKey: metric.key // 添加字段标识
        }
        this.timePeriods.forEach(period => {
          const dataItem = this.detailData.data[period.dataIndex]
          row[period.key] = dataItem ? (dataItem[metric.key] || 0) : 0
        })
        return row
      })
    },

    // 广告数据表格
    adTableData() {
      if (!this.detailData || !this.detailData.data || !Array.isArray(this.detailData.data)) return []

      const adMetrics = [
        { key: 'targetAdVolume', label: '目标广告订单' },
        { key: 'actualAdVolume', label: '实际广告订单' },
        { key: 'targetAdVolumeRatio', label: '目标广告订单占比' },
        { key: 'actualAdVolumeRatio', label: '实际广告订单占比' },
        { key: 'targetWeeklyAdSpending', label: '目标广告花费（周）' },
        { key: 'actualWeeklyAdSpending', label: '实际广告花费（周）' },
        { key: 'actualTotalSpending', label: '实际总花费' },
        { key: 'adSpending', label: '广告花费' },
        { key: 'targetBCPA', label: '目标广告CPA' },
        { key: 'cpa', label: '实际CPA' },
        { key: 'targetBCVR', label: '目标广告CVR' },
        { key: 'adCVR', label: '广告CVR' },
        { key: 'adImpressions', label: '广告曝光次数' },
        { key: 'totalClicks', label: '点击次数' },
        { key: 'ctr', label: 'CTR' },
        { key: 'cpc', label: '广告CPC' },
        { key: 'acos', label: 'ACOS' },
        { key: 'targetACOAS', label: '目标ACOAS' },
        { key: 'actualACOAS', label: '实际ACOAS' }
      ]

      return adMetrics.map(metric => {
        const row = {
          label: metric.label,
          fieldKey: metric.key // 添加字段标识
        }
        this.timePeriods.forEach(period => {
          const dataItem = this.detailData.data[period.dataIndex]
          row[period.key] = dataItem ? (dataItem[metric.key] || 0) : 0
        })
        return row
      })
    },

    // 利润数据表格
    profitTableData() {
      if (!this.detailData || !this.detailData.data || !Array.isArray(this.detailData.data)) return []

      const profitMetrics = [
        { key: 'targetWeeklyAccumulatedProfit', label: '目标毛利润（周）' },
        { key: 'actualWeeklyProfit', label: '实际毛利润（周）' },
        { key: 'totalWeeklyProfit', label: '总周利润' },
        { key: 'targetWeeklyAccumulatedProfitRate', label: '目标毛利率（周）' },
        { key: 'actualWeeklyProfitRate', label: '实际毛利率（周）' }
      ]

      return profitMetrics.map(metric => {
        const row = {
          label: metric.label,
          fieldKey: metric.key // 添加字段标识
        }
        this.timePeriods.forEach(period => {
          const dataItem = this.detailData.data[period.dataIndex]
          row[period.key] = dataItem ? (dataItem[metric.key] || 0) : 0
        })
        return row
      })
    },

    // Listing数据表格
    listingTableData() {
      if (!this.detailData || !this.detailData.data || !Array.isArray(this.detailData.data)) return []

      const listingMetrics = [
        { key: 'reviewCount', label: '评论数' },
        { key: 'reviewRating', label: '评论星级' }
      ]

      return listingMetrics.map(metric => {
        const row = {
          label: metric.label,
          fieldKey: metric.key // 添加字段标识
        }
        this.timePeriods.forEach(period => {
          const dataItem = this.detailData.data[period.dataIndex]
          row[period.key] = dataItem ? (dataItem[metric.key] || 0) : 0
        })
        return row
      })
    }
  },
  created() {
    this.initFromRoute()
    this.initDateRange()
    this.loadDetailData()
  },
  methods: {
    // 从路由参数初始化产品信息
    initFromRoute() {
      // 从路由参数获取产品信息
      if (this.$route.query.productInfo) {
        try {
          this.productInfo = JSON.parse(decodeURIComponent(this.$route.query.productInfo))
        } catch (error) {
          console.error('解析产品信息失败:', error)
          this.$message.error('产品信息获取失败')
        }
      }

      // 从路由参数获取日期范围
      if (this.$route.query.startDate && this.$route.query.endDate) {
        this.dateRange.startDate = this.$route.query.startDate
        this.dateRange.endDate = this.$route.query.endDate
      }
    },

    // 初始化日期范围为当前月份
    initDateRange() {
      if (!this.dateRange.startDate || !this.dateRange.endDate) {
        const now = new Date()
        const year = now.getFullYear()
        const month = now.getMonth()

        // 当前月份第一天
        const firstDay = new Date(year, month, 1)
        // 当前月份最后一天
        const lastDay = new Date(year, month + 1, 0)

        this.dateRange.startDate = this.formatDate(firstDay)
        this.dateRange.endDate = this.formatDate(lastDay)
      }
    },

    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },

    // 加载详情数据
    loadDetailData() {
      if (!this.productInfo) {
        this.$message.error('产品信息缺失')
        return
      }

      this.detailLoading = true
      const params = {
        productId: this.productInfo.asin || this.productInfo.sellerSku,
        startDate: this.dateRange.startDate,
        endDate: this.dateRange.endDate
      }

      getWeeklyReviewDetail(params).then(res => {
        console.log('API响应数据:', res)
        if (res.success === true) {
          this.detailData = res
          // 处理可编辑字段列表
          this.editableColumns = res.editableColumns || []
          console.log('设置detailData:', this.detailData)
          console.log('可编辑字段:', this.editableColumns)
        } else {
          this.$message.error('获取详情数据失败')
          this.detailData = null
          this.editableColumns = []
        }
        this.detailLoading = false
      }).catch(error => {
        console.error('获取详情数据失败:', error)
        this.$message.error('获取详情数据失败')
        this.detailData = null
        this.editableColumns = []
        this.detailLoading = false
      })
    },

    // 导出数据
    exportData() {
      if (!this.productInfo) {
        this.$message.warning('请先选择产品')
        return
      }

      if (!this.dateRange.startDate || !this.dateRange.endDate) {
        this.$message.warning('请选择日期范围')
        return
      }

      this.detailLoading = true
      const params = {
        productId: this.productInfo.asin || this.productInfo.sellerSku,
        startDate: this.dateRange.startDate,
        endDate: this.dateRange.endDate
      }

      exportWeeklyReview(params).then(res => {
        downloadFile(res, '产品绩效周度回顾', 'xlsx')
        this.detailLoading = false
        this.$notify({
          title: '导出成功',
          type: 'success',
          duration: 2500
        })
      }).catch(error => {
        console.error('导出数据失败:', error)
        this.$message.error('导出数据失败')
        this.detailLoading = false
      })
    },

    // 单元格双击编辑事件
    cellEdit(row, column) {
      // 跳过第一列（标签列）的编辑
      if (column.property === 'label') {
        return
      }

      // 检查该字段是否允许编辑
      if (!this.isFieldEditable(row.fieldKey)) {
        this.$message.warning('该字段不允许编辑')
        return
      }

      // 设置编辑状态
      this.$set(row, 'isEdit', true)
      this.editingCell = {
        row: row,
        column: column.property
      }

      // 聚焦到输入框
      this.$nextTick(() => {
        const ref = this.$refs[column.property]
        if (ref && ref.length > 0) {
          ref[0].focus()
        }
      })

      console.log('开始编辑单元格:', {
        row: row.label,
        column: column.property,
        value: row[column.property],
        fieldKey: row.fieldKey,
        isEditable: this.isFieldEditable(row.fieldKey)
      })
    },

    // 失去焦点时关闭编辑状态
    onBlur(row, columnProperty) {
      row.isEdit = false
      this.editingCell = {
        row: null,
        column: null
      }

      console.log('编辑完成:', {
        row: row.label,
        column: columnProperty,
        newValue: row[columnProperty]
      })
    },

    // 判断字段是否可编辑
    isFieldEditable(fieldKey) {
      if (!fieldKey || !Array.isArray(this.editableColumns)) {
        return false
      }
      return this.editableColumns.includes(fieldKey)
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .header-left {
    display: flex;
    align-items: center;

    .page-title {
      margin: 0 0 0 15px;
      color: #303133;
      font-size: 20px;
      font-weight: 600;
    }
  }

  .header-right {
    display: flex;
    align-items: center;
  }
}

.product-info-card {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  h3 {
    margin: 0 0 15px 0;
    color: #303133;
    font-size: 18px;
    font-weight: 600;
  }

  .info-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;

    .info-item {
      display: flex;
      align-items: center;
      min-width: 200px;

      .label {
        font-weight: 600;
        color: #606266;
        margin-right: 8px;
      }

      .value {
        color: #303133;
      }
    }
  }
}

.date-picker-card {
  background-color: #fff;
  padding: 15px 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .date-picker-container {
    display: flex;
    align-items: center;
  }
}

.detail-content {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-section {
  margin-bottom: 30px;

  &:last-child {
    margin-bottom: 0;
  }

  h4 {
    margin: 0 0 15px 0;
    color: #303133;
    font-size: 16px;
    font-weight: 600;
    padding-bottom: 8px;
    border-bottom: 2px solid #409eff;
  }
}

.no-data {
  text-align: center;
  padding: 50px;
  color: #909399;
  font-size: 16px;
}

// 可编辑单元格样式
.editable-cell {
  color: #f56c6c !important;
  font-weight: 500;
  cursor: pointer;
  background-color: #fce4ec !important; // 淡粉色背景
  padding: 4px 8px;
  border-radius: 3px;

  &:hover {
    color: #e6a23c !important;
    background-color: #f8bbd9 !important; // 悬停时稍深的粉色
  }
}

// 表格样式优化
::v-deep .el-table {
  .el-table__header-wrapper {
    th {
      background-color: #fafafa;
      color: #303133;
      font-weight: 600;
    }
  }

  .el-table__body-wrapper {
    td {
      &:first-child {
        background-color: #f9f9f9;
        font-weight: 500;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;

    .header-left {
      .page-title {
        margin-left: 10px;
        font-size: 18px;
      }
    }
  }

  .product-info-card {
    .info-row {
      flex-direction: column;
      gap: 10px;

      .info-item {
        min-width: auto;
        flex-direction: column;
        align-items: flex-start;

        .label {
          margin-right: 0;
          margin-bottom: 4px;
        }
      }
    }
  }

  .date-picker-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;

    .el-date-editor {
      width: 100% !important;
      margin-right: 0 !important;
    }

    .el-button {
      width: 100%;
    }
  }
}
</style>
